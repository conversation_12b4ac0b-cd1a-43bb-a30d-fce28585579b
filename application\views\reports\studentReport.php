<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-line-chart"></i> <?php //echo $this->lang->line('reports'); ?> <small> <?php //echo $this->lang->line('filter_by_name1'); ?></small></h1>
    </section>
    <!-- Main content -->
    <section class="content" >
        <?php $this->load->view('reports/_studentinformation');?>
        <div class="row">
            <div class="col-md-12">
                <div class="box removeboxmius">
                    <div class="box-header ptbnull"></div>
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
                    </div>
                    <div class="box-body">
                        <form role="form" action="<?php echo site_url('report/studentreportvalidation') ?>" method="post" class="" id="reportform">
                            <div class="row">
                                <?php echo $this->customlib->getCSRF(); ?>
                                <div class="col-sm-6 col-md-3">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('class'); ?></label><small class="req"> *</small>
                                        <select autofocus="" id="class_id" name="class_id[]" class="form-control multiselect-dropdown" multiple="multiple">
                                            <?php
foreach ($classlist as $class) {
    ?>
                                                <option value="<?php echo $class['id'] ?>" <?php if (set_value('class_id') == $class['id']) {
        echo "selected=selected";
    }
    ?>><?php echo $class['class'] ?></option>
                                                <?php
$count++;
}
?>
                                        </select>
                                         <span class="text-danger" id="error_class_id"></span>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-3">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('section'); ?></label>
                                        <select  id="section_id" name="section_id[]" class="form-control multiselect-dropdown" multiple="multiple">
                                        </select>
                                        <span class="text-danger"><?php echo form_error('section_id'); ?></span>
                                    </div>
                                </div>
                                <?php if ($sch_setting->category) {
    ?>
                                    <div class="col-sm-3 col-md-2">
                                        <div class="form-group">
                                            <label><?php echo $this->lang->line('category'); ?></label>
                                            <select  id="category_id" name="category_id[]" class="form-control multiselect-dropdown" multiple="multiple">
                                                <?php
foreach ($categorylist as $category) {
        ?>
                                                    <option value="<?php echo $category['id'] ?>" <?php if (set_value('category_id') == $category['id']) {
            echo "selected=selected";
        }
        ?>><?php echo $category['category'] ?></option>
                                                    <?php
$count++;
    }
    ?>
                                            </select>
                                        </div>
                                    </div>
                                <?php }?>
                                <div class="col-sm-3 col-md-2">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('gender'); ?></label>
                                        <select class="form-control multiselect-dropdown" name="gender[]" multiple="multiple">
                                            <?php
foreach ($genderList as $key => $value) {
    ?>
                                                <option value="<?php echo $key; ?>" <?php if (set_value('gender') == $key) {
        echo "selected";
    }
    ?>><?php echo $value; ?></option>
                                                <?php
}
?>
                                        </select>
                                    </div>
                                </div>
                                <?php if ($sch_setting->rte) {
    ?>
                                    <div class="col-sm-3 col-md-2">
                                        <div class="form-group">
                                            <label><?php echo $this->lang->line('rte'); ?></label>
                                            <select  id="rte" name="rte[]" class="form-control multiselect-dropdown" multiple="multiple">
                                                <?php
foreach ($RTEstatusList as $k => $rte) {
        ?>
                                                    <option value="<?php echo $k; ?>" <?php if (set_value('rte') == $k) {
            echo "selected";
        }
        ?>><?php echo $rte; ?></option>

                                                    <?php
$count++;
    }
    ?>
                                            </select>
                                        </div>
                                    </div>
                                <?php }?>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <button type="submit" name="search" value="search_filter" class="btn btn-primary btn-sm checkbox-toggle pull-right"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                                    </div>
                                </div>
                            </div><!--./row-->
                        </form>
                    </div><!--./box-body-->
                        <div class="">
                            <div class="box-header ptbnull"></div>
                            <div class="box-header ptbnull">
                                <h3 class="box-title titlefix"><i class="fa fa-users"></i> <?php echo form_error('student'); ?> <?php echo $this->lang->line('student_report'); ?></h3>
                            </div>
                            <div class="box-body table-responsive">
                                    <div class="download_label"> <?php echo $this->lang->line('student_report'); ?></div>
                            <div >
                                <table class="table table-striped table-bordered table-hover student-list" data-export-title="<?php echo $this->lang->line('student_report'); ?>">
                                    <thead>
                                        <tr>
                                            <th><?php echo $this->lang->line('section'); ?></th>
                                            <th><?php echo $this->lang->line('admission_no'); ?></th>
                                            <th><?php echo $this->lang->line('student_name'); ?></th>
                                            <?php if ($sch_setting->father_name) {?>
                                                <th><?php echo $this->lang->line('father_name'); ?></th>
                                                <?php }?>
                                            <th><?php echo $this->lang->line('date_of_birth'); ?></th>
                                            <th><?php echo $this->lang->line('gender'); ?></th>
                                            <?php if ($sch_setting->category) {?>
                                                <th><?php echo $this->lang->line('category'); ?></th>
                                            <?php }if ($sch_setting->mobile_no) {?>
                                                <th><?php echo $this->lang->line('mobile_number'); ?></th>
                                            <?php
}
if ($sch_setting->local_identification_no) {
    ?>
                                                <th><?php echo $this->lang->line('local_identification_number'); ?></th>
                                            <?php }if ($sch_setting->national_identification_no) {?>
                                                <th><?php echo $this->lang->line('national_identification_number'); ?></th>
                                            <?php }if ($sch_setting->rte) {?>
                                                <th><?php echo $this->lang->line('rte'); ?></th>
                                            <?php }?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div><!--./box box-primary -->
            </div><!-- ./col-md-12 -->
        </div>
</div>
</section>
</div>

<!-- Include SumoSelect CSS and JS -->
<link href="<?php echo base_url(); ?>assets1/vendor/bootstrap-multiselect/sumoselect.min.css" rel="stylesheet"/>
<script src="<?php echo base_url(); ?>assets1/vendor/bootstrap-multiselect/jquery.sumoselect.min.js"></script>

<script type="text/javascript">
    function getSectionByClass(class_ids, section_id) {
        if (class_ids != "" && section_id != "") {
            var sectionDropdown = $('#section_id')[0];
            if (sectionDropdown && sectionDropdown.sumo) {
                sectionDropdown.sumo.removeAll();
            }

            var base_url = '<?php echo base_url() ?>';

            // Handle both single value and array
            if (!Array.isArray(class_ids)) {
                class_ids = [class_ids];
            }

            var requests = [];
            $.each(class_ids, function(index, class_id) {
                requests.push(
                    $.ajax({
                        type: "GET",
                        url: base_url + "sections/getByClass",
                        data: {'class_id': class_id},
                        dataType: "json"
                    })
                );
            });

            $.when.apply($, requests).done(function() {
                var allSections = [];
                var addedSections = {};

                // Process results from all requests
                for (var i = 0; i < arguments.length; i++) {
                    var data = requests.length === 1 ? arguments[0] : arguments[i][0];
                    if (data && Array.isArray(data)) {
                        $.each(data, function (j, obj) {
                            if (!addedSections[obj.section_id]) {
                                allSections.push(obj);
                                addedSections[obj.section_id] = true;
                            }
                        });
                    }
                }

                // Add options to section dropdown
                if (sectionDropdown && sectionDropdown.sumo) {
                    $.each(allSections, function (i, obj) {
                        var selected = (section_id == obj.section_id);
                        sectionDropdown.sumo.add(obj.section_id, obj.section, selected);
                    });
                }
            });
        }
    }

    $(document).ready(function () {
        console.log('Document ready, jQuery version:', $.fn.jquery);
        console.log('Found multiselect dropdowns:', $('.multiselect-dropdown').length);

        // Check if SumoSelect is available
        if (typeof $.fn.SumoSelect === 'undefined') {
            console.error('SumoSelect plugin not loaded!');
            return;
        }

        // Initialize SumoSelect for all multi-select dropdowns
        $('.multiselect-dropdown').SumoSelect({
            placeholder: 'Select Options',
            csvDispCount: 3,
            captionFormat: '{0} Selected',
            captionFormatAllSelected: 'All Selected',
            selectAll: true,
            search: true,
            searchText: 'Search...',
            noMatch: 'No matches found',
            okCancelInMulti: true,
            isClickAwayOk: true
        });

        console.log('SumoSelect initialized for', $('.multiselect-dropdown').length, 'elements');

        var class_id = $('#class_id').val();
        var section_id = '<?php echo set_value('section_id') ?>';
        getSectionByClass(class_id, section_id);

        $(document).on('change', '#class_id', function (e) {
            var sectionDropdown = $('#section_id')[0];
            if (sectionDropdown && sectionDropdown.sumo) {
                sectionDropdown.sumo.removeAll();
            }

            var class_ids = $(this).val();
            var base_url = '<?php echo base_url() ?>';

            if (class_ids && class_ids.length > 0) {
                var requests = [];

                // Get sections for all selected classes
                $.each(class_ids, function(index, class_id) {
                    requests.push(
                        $.ajax({
                            type: "GET",
                            url: base_url + "sections/getByClass",
                            data: {'class_id': class_id},
                            dataType: "json"
                        })
                    );
                });

                // Wait for all requests to complete
                $.when.apply($, requests).done(function() {
                    var allSections = [];
                    var addedSections = {};

                    // Process results from all requests
                    for (var i = 0; i < arguments.length; i++) {
                        var data = requests.length === 1 ? arguments[0] : arguments[i][0];
                        if (data && Array.isArray(data)) {
                            $.each(data, function (j, obj) {
                                if (!addedSections[obj.section_id]) {
                                    allSections.push(obj);
                                    addedSections[obj.section_id] = true;
                                }
                            });
                        }
                    }

                    // Add options to section dropdown
                    if (sectionDropdown && sectionDropdown.sumo) {
                        $.each(allSections, function (i, obj) {
                            sectionDropdown.sumo.add(obj.section_id, obj.section);
                        });
                    }
                });
            }
        });
    });
</script>

<script>
$(document).ready(function() {
    emptyDatatable('student-list','data');
});
</script>

<script type="text/javascript">
$(document).ready(function(){
$(document).on('submit','#reportform',function(e){
    e.preventDefault(); // avoid to execute the actual submit of the form.
    var $this = $(this).find("button[type=submit]:focus");
    var form = $(this);
    var url = form.attr('action');

    // Custom serialization to handle multi-select properly
    var form_data = [];

    // Get all form elements
    form.find('input, select, textarea').each(function() {
        var $element = $(this);
        var name = $element.attr('name');
        var type = $element.attr('type');

        if (name) {
            if ($element.is('select[multiple]')) {
                // Handle multi-select dropdowns
                var values = $element.val();
                if (values && values.length > 0) {
                    $.each(values, function(index, value) {
                        form_data.push({name: name, value: value});
                    });
                }
            } else if (type !== 'submit' && type !== 'button') {
                // Handle other form elements
                form_data.push({name: name, value: $element.val()});
            }
        }
    });

    form_data.push({name: 'search_type', value: $this.attr('value')});

    $.ajax({
           url: url,
           type: "POST",
           dataType:'JSON',
           data: form_data,
              beforeSend: function () {
                $('[id^=error]').html("");
                $this.button('loading');

               },
              success: function(response) { // your success handler

                if(!response.status){
                    $.each(response.error, function(key, value) {
                    $('#error_' + key).html(value);
                    });
                }else{

                   initDatatable('student-list','report/dtstudentreportlist',response.params,[],100);
                }
              },
             error: function() { // your error handler
                 $this.button('reset');
             },
             complete: function() {
             $this.button('reset');
             }
         });
        });
    });
</script>