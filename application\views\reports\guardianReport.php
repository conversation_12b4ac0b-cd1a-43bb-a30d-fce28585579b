<?php
$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
?>
<style type="text/css">
    /*REQUIRED*/
    .carousel-row {
        margin-bottom: 10px;
    }
    .slide-row {
        padding: 0;
        background-color: #ffffff;
        min-height: 150px;
        border: 1px solid #e7e7e7;
        overflow: hidden;
        height: auto;
        position: relative;
    }
    .slide-carousel {
        width: 20%;
        float: left;
        display: inline-block;
    }
    .slide-carousel .carousel-indicators {
        margin-bottom: 0;
        bottom: 0;
        background: rgba(0, 0, 0, .5);
    }
    .slide-carousel .carousel-indicators li {
        border-radius: 0;
        width: 20px;
        height: 6px;
    }
    .slide-carousel .carousel-indicators .active {
        margin: 1px;
    }
    .slide-content {
        position: absolute;
        top: 0;
        left: 20%;
        display: block;
        float: left;
        width: 80%;
        max-height: 76%;
        padding: 1.5% 2% 2% 2%;
        overflow-y: auto;
    }
    .slide-content h4 {
        margin-bottom: 3px;
        margin-top: 0;
    }
    .slide-footer {
        position: absolute;
        bottom: 0;
        left: 20%;
        width: 78%;
        height: 20%;
        margin: 1%;
    }
    /* Scrollbars */
    .slide-content::-webkit-scrollbar {
        width: 5px;
    }
    .slide-content::-webkit-scrollbar-thumb:vertical {
        margin: 5px;
        background-color: #999;
        -webkit-border-radius: 5px;
    }
    .slide-content::-webkit-scrollbar-button:start:decrement,
    .slide-content::-webkit-scrollbar-button:end:increment {
        height: 5px;
        display: block;
    }
</style>

<div class="content-wrapper" style="min-height: 946px;">
    <section class="content-header">
        <h1>
            <i class="fa fa-user-plus"></i> <?php //echo $this->lang->line('student_information'); ?>
        </h1>
    </section>
    <!-- Main content -->
    <section class="content">
        <?php $this->load->view('reports/_studentinformation');?>
        <div class="row">
            <div class="col-md-12">
                <div class="box removeboxmius">
                    <div class="box-header ptbnull"></div>
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
                    </div>
                    <div class="box-body">
                        <form role="form" action="<?php echo site_url('report/guardianreport') ?>" method="post" class="" data-multiselect="true">
                            <div class="row">
                                <?php echo $this->customlib->getCSRF(); ?>
                                <div class="col-sm-6 col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('class'); ?></label><small class="req"> *</small>
                                        <select autofocus="" id="class_id" name="class_id[]" class="form-control multiselect-dropdown" multiple="multiple">
                                            <?php
foreach ($classlist as $class) {
    ?>
                                                <option value="<?php echo $class['id'] ?>" <?php if (set_value('class_id') == $class['id']) {
        echo "selected=selected";
    }
    ?> ><?php echo $class['class'] ?></option>
                                                <?php
$count++;
}
?>
                                        </select>
                                        <span class="text-danger" id="error_class_id"><?php echo form_error('class_id'); ?></span>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('section'); ?></label>
                                        <select  id="section_id" name="section_id[]" class="form-control multiselect-dropdown" multiple="multiple">
                                        </select>
                                        <span class="text-danger" id="error_section_id"><?php echo form_error('section_id'); ?></span>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <button type="submit" name="search" value="search_filter" class="btn btn-primary btn-sm pull-right"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                                    </div>
                                </div>
                            </div><!--./row-->
                        </form>
                    </div><!--./box-body-->
                    <div class="">
                        <div class="box-header ptbnull"></div>
                        <div class="box-header ptbnull">
                            <h3 class="box-title titlefix"><i class="fa fa-users"></i> <?php echo form_error('student'); ?> <?php echo $this->lang->line('guardian_report'); ?></h3>
                        </div>
                        <div class="box-body table-responsive">
                            <div class="download_label"><?php
echo $this->lang->line('guardian_report') . "<br>";
$this->customlib->get_postmessage();
?></div>
                            <table class="table table-striped table-bordered table-hover example">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('class_section'); ?></th>
                                        <th><?php echo $this->lang->line('admission_no'); ?></th>
                                        <th><?php echo $this->lang->line('student_name'); ?></th>
                                        <?php if ($sch_setting->mobile_no) {?>
                                            <th><?php echo $this->lang->line('mobile_number'); ?></th>
                                        <?php }if ($sch_setting->guardian_name) {?>
                                        <th><?php echo $this->lang->line('guardian_name'); ?></th>
                                        <?php }if ($sch_setting->guardian_relation) {?>
                                            <th><?php echo $this->lang->line('guardian_relation'); ?></th>
                                        <?php }if ($sch_setting->guardian_phone) {?>
                                        <th><?php echo $this->lang->line('guardian_phone'); ?></th>
                                        <?php }if ($sch_setting->father_name) {?>
                                            <th><?php echo $this->lang->line('father_name'); ?></th>
                                        <?php }if ($sch_setting->father_phone) {?>
                                            <th><?php echo $this->lang->line('father_phone'); ?></th>
                                        <?php }if ($sch_setting->mother_name) {?>
                                            <th><?php echo $this->lang->line('mother_name'); ?></th>
                                        <?php }if ($sch_setting->mother_phone) {?>
                                            <th><?php echo $this->lang->line('mother_phone'); ?></th>
<?php }?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($resultlist)) {?>
                                    <?php
} else {
    $count = 1;
    foreach ($resultlist as $student) {
        ?>
                                            <tr>
                                                <td><?php echo $student['class'] . " (" . $student["section"] . ")"; ?></td>
                                                <td><?php echo $student['admission_no']; ?></td>
                                                <td>
                                                    <a href="<?php echo base_url(); ?>student/view/<?php echo $student['id']; ?>"><?php echo $this->customlib->getFullName($student['firstname'], $student['middlename'], $student['lastname'], $sch_setting->middlename, $sch_setting->lastname); ?></a>
                                                </td>
                                                <?php if ($sch_setting->mobile_no) {?>
                                                    <td><?php echo $student['mobileno']; ?></td>
                                                <?php }if ($sch_setting->guardian_name) {?>
                                                <td><?php echo $student['guardian_name']; ?></td>
                                                <?php }if ($sch_setting->guardian_relation) {?>
                                                    <td><?php echo $student['guardian_relation']; ?></td>
                                                 <?php }if ($sch_setting->guardian_phone) {?>
                                                <td><?php echo $student['guardian_phone']; ?></td>
                                                <?php }if ($sch_setting->father_name) {?>
                                                    <td><?php echo $student['father_name']; ?></td>
                                                <?php }if ($sch_setting->father_phone) {?>
                                                    <td><?php echo $student['father_phone']; ?></td>
                                                <?php }if ($sch_setting->mother_name) {?>
                                                    <td><?php echo $student['mother_name']; ?></td>
                                                <?php }if ($sch_setting->mother_phone) {?>
                                                    <td><?php echo $student['mother_phone']; ?></td>
                                            <?php }?>
                                            </tr>
                                            <?php
$count++;
    }
}
?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div><!--./box box-primary-->
            </div><!--./col-md-12-->
        </div>
    </div>
</section>
</div>

<!-- Include SumoSelect CSS and JS -->
<link href="<?php echo base_url(); ?>assets1/vendor/bootstrap-multiselect/sumoselect.min.css" rel="stylesheet"/>
<script src="<?php echo base_url(); ?>assets1/vendor/bootstrap-multiselect/jquery.sumoselect.min.js"></script>
<script src="<?php echo base_url(); ?>backend/js/multiselect-reports.js"></script>

<script type="text/javascript">
    $(document).ready(function () {
        // Multi-select functionality is handled by multiselect-reports.js
        console.log('Guardian Report: Multi-select functionality initialized');

        // Initialize sections for any pre-selected classes
        var class_id = $('#class_id').val();
        var section_id = '<?php echo set_value('section_id') ?>';
        if (class_id && section_id) {
            MultiselectReports.getSectionByClass(class_id, section_id);
        }
    });
</script>