<?php
$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
?>
<style type="text/css">
    /*REQUIRED*/
    .carousel-row {
        margin-bottom: 10px;
    }
    .slide-row {
        padding: 0;
        background-color: #ffffff;
        min-height: 150px;
        border: 1px solid #e7e7e7;
        overflow: hidden;
        height: auto;
        position: relative;
    }
    .slide-carousel {
        width: 20%;
        float: left;
        display: inline-block;
    }
    .slide-carousel .carousel-indicators {
        margin-bottom: 0;
        bottom: 0;
        background: rgba(0, 0, 0, .5);
    }
    .slide-carousel .carousel-indicators li {
        border-radius: 0;
        width: 20px;
        height: 6px;
    }
    .slide-carousel .carousel-indicators .active {
        margin: 1px;
    }
    .slide-content {
        position: absolute;
        top: 0;
        left: 20%;
        display: block;
        float: left;
        width: 80%;
        max-height: 76%;
        padding: 1.5% 2% 2% 2%;
        overflow-y: auto;
    }
    .slide-content h4 {
        margin-bottom: 3px;
        margin-top: 0;
    }
    .slide-footer {
        position: absolute;
        bottom: 0;
        left: 20%;
        width: 78%;
        height: 20%;
        margin: 1%;
    }
    /* Scrollbars */
    .slide-content::-webkit-scrollbar {
        width: 5px;
    }
    .slide-content::-webkit-scrollbar-thumb:vertical {
        margin: 5px;
        background-color: #999;
        -webkit-border-radius: 5px;
    }
    .slide-content::-webkit-scrollbar-button:start:decrement,
    .slide-content::-webkit-scrollbar-button:end:increment {
        height: 5px;
        display: block;
    }

/* SumoSelect Multi-Select Dropdown Styling */
.SumoSelect {
    width: 100% !important;
}

.SumoSelect > .CaptionCont {
    border: 1px solid #d2d6de;
    border-radius: 3px;
    padding: 6px 12px;
    background-color: #fff;
    min-height: 34px;
    line-height: 1.42857143;
}

.SumoSelect > .CaptionCont > label {
    margin: 0;
    font-weight: normal;
    color: #555;
}

.SumoSelect .optWrapper {
    border: 1px solid #d2d6de;
    border-top: none;
    border-radius: 0 0 3px 3px;
    background-color: #fff;
    max-height: 200px;
    overflow-y: auto;
    z-index: 9999;
}

.SumoSelect .optWrapper .options {
    max-height: 200px;
    overflow-y: auto;
}

.SumoSelect .optWrapper .options li {
    padding: 6px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f4f4f4;
}

.SumoSelect .optWrapper .options li:hover {
    background-color: #f5f5f5;
}

.SumoSelect .optWrapper .options li.selected {
    background-color: #3c8dbc;
    color: white;
}

/* Search box styling */
.SumoSelect .search-box {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
}

.SumoSelect .search-box input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #d2d6de;
    border-radius: 3px;
    font-size: 12px;
}

/* Select all/clear all button styling */
.SumoSelect .select-all {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 8px 12px;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: block !important;
}

.SumoSelect .select-all:hover {
    background-color: #e9ecef;
}

/* Ensure Select All option is visible */
.SumoSelect .optWrapper .options li.opt {
    display: list-item !important;
    padding: 6px 12px;
    cursor: pointer;
}

.SumoSelect .optWrapper .options li.opt:hover {
    background-color: #f5f5f5;
}

/* Select All specific styling */
.SumoSelect .optWrapper .options li.opt.select-all {
    background-color: #e3f2fd;
    border-bottom: 1px solid #bbdefb;
    font-weight: 600;
    color: #1976d2;
}

.SumoSelect .optWrapper .options li.opt.select-all:hover {
    background-color: #bbdefb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .SumoSelect .optWrapper {
        max-height: 150px;
    }

    .SumoSelect .optWrapper .options {
        max-height: 150px;
    }
}

/* Success and error message styling */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

.alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit;
}
</style>

<div class="content-wrapper" style="min-height: 946px;">
    <section class="content-header">
        <h1>
            <i class="fa fa-user-plus"></i> <?php //echo $this->lang->line('student_information'); ?>
        </h1>
    </section>
    <!-- Main content -->
    <section class="content">
        <?php $this->load->view('reports/_studentinformation');?>
        <div class="row">
            <div class="col-md-12">
                <div class="box removeboxmius">
                    <div class="box-header ptbnull"></div>
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
                    </div>
                    <div class="box-body">
                        <form role="form" action="<?php echo site_url('report/guardianreportvalidation') ?>" method="post" class="" id="reportform">
                            <div class="row">
                                <?php echo $this->customlib->getCSRF(); ?>
                                <div class="col-sm-6 col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('class'); ?></label>
                                        <select autofocus="" id="class_id" name="class_id[]" class="form-control multiselect-dropdown" multiple>
                                            <?php
foreach ($classlist as $class) {
    ?>
                                                <option value="<?php echo $class['id'] ?>" <?php if (set_value('class_id') == $class['id']) {
        echo "selected=selected";
    }
    ?> ><?php echo $class['class'] ?></option>
                                                <?php
$count++;
}
?>
                                        </select>
                                        <span class="text-danger" id="error_class_id"></span>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('section'); ?></label>
                                        <select id="section_id" name="section_id[]" class="form-control multiselect-dropdown" multiple>
                                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                                        </select>
                                        <span class="text-danger" id="error_section_id"></span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <button type="submit" name="search" value="search_filter" class="btn btn-primary btn-sm checkbox-toggle pull-right"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                                    </div>
                                </div>
                            </div><!--./row-->
                        </form>
                    </div><!--./box-body-->
                    <div class="">
                        <div class="box-header ptbnull"></div>
                        <div class="box-header ptbnull">
                            <h3 class="box-title titlefix"><i class="fa fa-users"></i> <?php echo form_error('student'); ?> <?php echo $this->lang->line('guardian_report'); ?></h3>
                        </div>
                        <div class="box-body table-responsive">
                            <div class="download_label"><?php
echo $this->lang->line('guardian_report') . "<br>";
$this->customlib->get_postmessage();
?></div>
                            <table class="table table-striped table-bordered table-hover" id="guardian-list" data-export-title="<?php echo $this->lang->line('guardian_report'); ?>">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('class_section'); ?></th>
                                        <th><?php echo $this->lang->line('admission_no'); ?></th>
                                        <th><?php echo $this->lang->line('student_name'); ?></th>
                                        <?php if ($sch_setting->mobile_no) {?>
                                            <th><?php echo $this->lang->line('mobile_number'); ?></th>
                                        <?php }if ($sch_setting->guardian_name) {?>
                                        <th><?php echo $this->lang->line('guardian_name'); ?></th>
                                        <?php }if ($sch_setting->guardian_relation) {?>
                                            <th><?php echo $this->lang->line('guardian_relation'); ?></th>
                                        <?php }if ($sch_setting->guardian_phone) {?>
                                        <th><?php echo $this->lang->line('guardian_phone'); ?></th>
                                        <?php }if ($sch_setting->father_name) {?>
                                            <th><?php echo $this->lang->line('father_name'); ?></th>
                                        <?php }if ($sch_setting->father_phone) {?>
                                            <th><?php echo $this->lang->line('father_phone'); ?></th>
                                        <?php }if ($sch_setting->mother_name) {?>
                                            <th><?php echo $this->lang->line('mother_name'); ?></th>
                                        <?php }if ($sch_setting->mother_phone) {?>
                                            <th><?php echo $this->lang->line('mother_phone'); ?></th>
<?php }?>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div><!--./box box-primary-->
            </div><!--./col-md-12-->
        </div>
    </div>
</section>
</div>

<script type="text/javascript">
$(document).ready(function(){
    // Initialize SumoSelect for all multi-select dropdowns
    $('.multiselect-dropdown').SumoSelect({
        placeholder: 'Select Options',
        csvDispCount: 3,
        captionFormat: '{0} Selected',
        captionFormatAllSelected: 'All Selected ({0})',
        selectAll: true,
        search: true,
        searchText: 'Search...',
        noMatch: 'No matches found "{0}"',
        okCancelInMulti: true,
        isClickAwayOk: true,
        locale: ['OK', 'Cancel', 'Select All'],
        up: false,
        showTitle: true
    });

    // Initialize section dropdown on page load if class is pre-selected
    var preSelectedClass = $('#class_id').val();
    if (preSelectedClass && preSelectedClass.length > 0) {
        $('#class_id').trigger('change');
    }

    // Handle class dropdown changes for section population
    $(document).on('change', '#class_id', function (e) {
        var sectionDropdown = $('#section_id')[0];
        if (sectionDropdown && sectionDropdown.sumo) {
            sectionDropdown.sumo.removeAll();
        }

        var class_ids = $(this).val();
        var base_url = '<?php echo base_url() ?>';

        if (class_ids && class_ids.length > 0) {
            var requests = [];
            var allSections = [];
            var addedSections = {};

            // Get sections for all selected classes
            $.each(class_ids, function(index, class_id) {
                requests.push(
                    $.ajax({
                        type: "GET",
                        url: base_url + "sections/getByClass",
                        data: {'class_id': class_id},
                        dataType: "json",
                        success: function(data) {
                            if (data && Array.isArray(data)) {
                                $.each(data, function(i, obj) {
                                    // Avoid duplicate sections
                                    if (!addedSections[obj.section_id]) {
                                        allSections.push({
                                            value: obj.section_id,
                                            text: obj.section
                                        });
                                        addedSections[obj.section_id] = true;
                                    }
                                });
                            }
                        }
                    })
                );
            });

            // Wait for all requests to complete
            $.when.apply($, requests).done(function() {
                // Add sections to dropdown
                if (sectionDropdown && sectionDropdown.sumo && allSections.length > 0) {
                    $.each(allSections, function(index, section) {
                        sectionDropdown.sumo.add(section.value, section.text);
                    });
                    // Refresh the dropdown to ensure proper display
                    sectionDropdown.sumo.reload();
                }
            });
        }
    });

    // Handle form submission with AJAX
    $(document).on('submit','#reportform',function(e){
        e.preventDefault(); // avoid to execute the actual submit of the form.
        var $this = $(this).find("button[type=submit]:focus");
        var form = $(this);
        var url = form.attr('action');

        // Use standard form serialization - works with both single and multi-select
        var form_data = form.serializeArray();
        form_data.push({name: 'search_type', value: $this.attr('value')});

        $.ajax({
            url: url,
            type: "POST",
            dataType:'JSON',
            data: form_data,
            beforeSend: function () {
                $('[id^=error]').html("");
                $this.button('loading');
            },
            success: function(response) { // your success handler
                if(!response.status){
                    // Clear any existing success messages
                    $('.alert-success').remove();

                    // Display validation errors
                    $.each(response.error, function(key, value) {
                        $('#error_' + key).html(value);
                    });

                    // Show general error message if no specific field errors
                    if (Object.keys(response.error).length === 0) {
                        showErrorMessage('An error occurred while processing your request. Please try again.');
                    }
                }else{
                    $('[id^=error]').html("");
                    $('.alert-danger').remove();
                    initDatatable('guardian-list','report/dtguardianreportlist',response.params,[],100);
                    showSuccessMessage('Guardian report generated successfully!');
                }
            },
            error: function() { // your error handler
                $this.button('reset');
            },
            complete: function() {
                $this.button('reset');
            }
        });
    });

    // Helper functions for user feedback
    function showSuccessMessage(message) {
        var alertHtml = '<div class="alert alert-success alert-dismissible">' +
                       '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>' +
                       '<h4><i class="icon fa fa-check"></i> Success!</h4>' + message + '</div>';
        $('.box-body').prepend(alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.alert-success').fadeOut();
        }, 5000);
    }

    function showErrorMessage(message) {
        var alertHtml = '<div class="alert alert-danger alert-dismissible">' +
                       '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>' +
                       '<h4><i class="icon fa fa-ban"></i> Error!</h4>' + message + '</div>';
        $('.box-body').prepend(alertHtml);

        // Auto-dismiss after 8 seconds
        setTimeout(function() {
            $('.alert-danger').fadeOut();
        }, 8000);
    }
});
</script>