/**
 * Multi-select Enhancement Script for Student Information Reports
 * This script provides consistent multi-select functionality across all report pages
 */

var MultiselectReports = {
    
    // Configuration options
    config: {
        placeholder: 'Select Options',
        csvDispCount: 3,
        captionFormat: '{0} Selected',
        captionFormatAllSelected: 'All Selected',
        selectAll: true,
        search: true,
        searchText: 'Search...',
        noMatch: 'No matches found',
        okCancelInMulti: true,
        isClickAwayOk: true
    },

    // Initialize multi-select functionality
    init: function() {
        console.log('MultiselectReports: Initializing...');
        
        // Check if SumoSelect is available
        if (typeof $.fn.SumoSelect === 'undefined') {
            console.error('MultiselectReports: SumoSelect plugin not loaded!');
            return false;
        }

        // Initialize all multi-select dropdowns
        this.initializeDropdowns();
        
        // Setup class-section relationship handlers
        this.setupClassSectionHandlers();
        
        // Setup form submission handlers
        this.setupFormHandlers();
        
        console.log('MultiselectReports: Initialization complete');
        return true;
    },

    // Initialize SumoSelect on all multi-select dropdowns
    initializeDropdowns: function() {
        $('.multiselect-dropdown').each(function() {
            if (!$(this)[0].sumo) {
                $(this).SumoSelect(MultiselectReports.config);
            }
        });
        
        console.log('MultiselectReports: Initialized', $('.multiselect-dropdown').length, 'dropdowns');
    },

    // Setup class-section relationship handlers
    setupClassSectionHandlers: function() {
        $(document).on('change', '#class_id', function(e) {
            MultiselectReports.handleClassChange($(this));
        });
    },

    // Handle class dropdown changes
    handleClassChange: function($classDropdown) {
        var sectionDropdown = $('#section_id')[0];
        if (sectionDropdown && sectionDropdown.sumo) {
            sectionDropdown.sumo.removeAll();
        }

        var class_ids = $classDropdown.val();
        var base_url = baseurl || window.baseurl || '';

        if (class_ids && class_ids.length > 0) {
            var requests = [];

            // Get sections for all selected classes
            $.each(class_ids, function(index, class_id) {
                requests.push(
                    $.ajax({
                        type: "GET",
                        url: base_url + "sections/getByClass",
                        data: {'class_id': class_id},
                        dataType: "json"
                    })
                );
            });

            // Wait for all requests to complete
            $.when.apply($, requests).done(function() {
                var allSections = [];
                var addedSections = {};

                // Process results from all requests
                for (var i = 0; i < arguments.length; i++) {
                    var data = requests.length === 1 ? arguments[0] : arguments[i][0];
                    if (data && Array.isArray(data)) {
                        $.each(data, function(j, obj) {
                            if (!addedSections[obj.section_id]) {
                                allSections.push(obj);
                                addedSections[obj.section_id] = true;
                            }
                        });
                    }
                }

                // Add options to section dropdown
                if (sectionDropdown && sectionDropdown.sumo) {
                    $.each(allSections, function(i, obj) {
                        sectionDropdown.sumo.add(obj.section_id, obj.section);
                    });
                }
            });
        }
    },

    // Setup form submission handlers
    setupFormHandlers: function() {
        $(document).on('submit', 'form[data-multiselect="true"]', function(e) {
            MultiselectReports.handleFormSubmission($(this), e);
        });
    },

    // Handle form submissions with multi-select data
    handleFormSubmission: function($form, event) {
        event.preventDefault();
        
        var $submitButton = $form.find('button[type=submit]:focus');
        var url = $form.attr('action');
        
        // Custom serialization to handle multi-select properly
        var form_data = [];
        
        // Get all form elements
        $form.find('input, select, textarea').each(function() {
            var $element = $(this);
            var name = $element.attr('name');
            var type = $element.attr('type');
            
            if (name) {
                if ($element.is('select[multiple]')) {
                    // Handle multi-select dropdowns
                    var values = $element.val();
                    if (values && values.length > 0) {
                        $.each(values, function(index, value) {
                            form_data.push({name: name, value: value});
                        });
                    }
                } else if (type !== 'submit' && type !== 'button') {
                    // Handle other form elements
                    form_data.push({name: name, value: $element.val()});
                }
            }
        });
        
        // Add search type if button has value
        if ($submitButton.attr('value')) {
            form_data.push({name: 'search_type', value: $submitButton.attr('value')});
        }

        // Submit via AJAX
        $.ajax({
            url: url,
            type: "POST",
            dataType: 'JSON',
            data: form_data,
            beforeSend: function() {
                $('[id^=error]').html("");
                $submitButton.button('loading');
            },
            success: function(response) {
                if (!response.status) {
                    $.each(response.error, function(key, value) {
                        $('#error_' + key).html(value);
                    });
                } else {
                    // Handle successful response
                    if (response.params && typeof initDatatable === 'function') {
                        // For datatable-based reports
                        var tableClass = $form.data('table-class') || 'example';
                        var ajaxUrl = $form.data('ajax-url') || '';
                        initDatatable(tableClass, ajaxUrl, response.params, [], 100);
                    } else {
                        // For other types of reports, reload the page or handle as needed
                        window.location.reload();
                    }
                }
            },
            error: function() {
                $submitButton.button('reset');
            },
            complete: function() {
                $submitButton.button('reset');
            }
        });
    },

    // Convert existing single-select dropdowns to multi-select
    convertToMultiselect: function(selector) {
        $(selector).each(function() {
            var $select = $(this);
            
            // Add multi-select attributes
            $select.attr('multiple', 'multiple');
            $select.addClass('multiselect-dropdown');
            
            // Update name attribute to array format
            var name = $select.attr('name');
            if (name && !name.endsWith('[]')) {
                $select.attr('name', name + '[]');
            }
            
            // Initialize SumoSelect
            if (!$select[0].sumo) {
                $select.SumoSelect(this.config);
            }
        }.bind(this));
    },

    // Utility function to get section by class (for backward compatibility)
    getSectionByClass: function(class_ids, section_id) {
        if (class_ids != "" && section_id != "") {
            var sectionDropdown = $('#section_id')[0];
            if (sectionDropdown && sectionDropdown.sumo) {
                sectionDropdown.sumo.removeAll();
            }
            
            var base_url = baseurl || window.baseurl || '';
            
            // Handle both single value and array
            if (!Array.isArray(class_ids)) {
                class_ids = [class_ids];
            }
            
            var requests = [];
            $.each(class_ids, function(index, class_id) {
                requests.push(
                    $.ajax({
                        type: "GET",
                        url: base_url + "sections/getByClass",
                        data: {'class_id': class_id},
                        dataType: "json"
                    })
                );
            });
            
            $.when.apply($, requests).done(function() {
                var allSections = [];
                var addedSections = {};
                
                // Process results from all requests
                for (var i = 0; i < arguments.length; i++) {
                    var data = requests.length === 1 ? arguments[0] : arguments[i][0];
                    if (data && Array.isArray(data)) {
                        $.each(data, function(j, obj) {
                            if (!addedSections[obj.section_id]) {
                                allSections.push(obj);
                                addedSections[obj.section_id] = true;
                            }
                        });
                    }
                }
                
                // Add options to section dropdown
                if (sectionDropdown && sectionDropdown.sumo) {
                    $.each(allSections, function(i, obj) {
                        var selected = (section_id == obj.section_id);
                        sectionDropdown.sumo.add(obj.section_id, obj.section, selected);
                    });
                }
            });
        }
    }
};

// Auto-initialize when document is ready
$(document).ready(function() {
    MultiselectReports.init();
});

// Make functions available globally for backward compatibility
window.getSectionByClass = MultiselectReports.getSectionByClass;
window.MultiselectReports = MultiselectReports;
